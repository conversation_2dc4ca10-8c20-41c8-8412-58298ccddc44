# 🏀 Dynasty Basketball League UI

**Modern web interface for your fantasy basketball dynasty salary league**

Built specifically for your Google Sheets data: https://docs.google.com/spreadsheets/d/1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY/edit

## 🎯 **Features**

### **📊 League Overview Dashboard**
- **Real-time Statistics**: Total teams, players, average salary, teams over cap
- **Visual Salary Caps**: Color-coded team cards (green = under cap, red = over cap)
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Modern UI**: Clean, professional interface with smooth animations

### **🏀 Team Management**
- **All 30 NBA Teams**: Automatically loads rosters from your Google Sheets
- **Salary Breakdown**: Shows current salary usage vs. $110M cap
- **Player Rosters**: Complete player lists with positions and salaries
- **Sorting**: Teams sorted by total salary (highest first)

### **💰 Salary Cap Visualization**
- **Visual Progress Bars**: Instant salary cap usage visualization
- **Cap Status Indicators**: Clear over/under cap status
- **Precise Calculations**: Exact salary amounts and cap space
- **Multi-year Support**: Built for dynasty league contract tracking

### **📱 Data Integration**
- **Google Sheets Import**: Direct integration with your spreadsheet
- **Live Updates**: Refresh data with one click
- **Export Functionality**: Download league data as JSON
- **Error Handling**: Graceful fallback to demo data

## 🚀 **Quick Start**

### **1. Start the Server**
```bash
PORT=3004 node dynasty-server.js
```

### **2. Open the Dashboard**
```
http://localhost:3004
```

### **3. Load Your League Data**
Click "📊 Load League Data" to import all team rosters from Google Sheets

## 📊 **Google Sheets Integration**

### **Expected Sheet Structure**
Your Google Sheets should have:
- **One sheet per team** (ATL, BOS, BRK, etc.)
- **Salary cap information** at the top
- **Player roster data** below

### **Sheet Format Example**
```
Toronto Raptors          | Salary Cap | Expenses | Available Cap
2026                     | $110m      | $95.4m   | $14.6m
2027                     | $110m      | $23.3m   | $86.7m

NBA Roster
Position | Name          | 2026    | 2027 | 2028
PG/SG    | Terry Rozier  | $19.0m  |      |
SF       | RJ Barrett    | $23.0m  |      |
```

### **Automatic Parsing**
The system automatically:
- **Extracts salary cap limits** ($110M)
- **Calculates current expenses** ($95.4M)
- **Parses player rosters** with positions and salaries
- **Handles multiple contract years** (focuses on 2026)

## 🎮 **How to Use**

### **Loading Data**
1. Click **"📊 Load League Data"**
2. System fetches all 30 team sheets from Google Sheets
3. Teams appear as interactive cards with salary information

### **Viewing Teams**
- **Team Cards**: Each team shows name, total salary, and cap status
- **Salary Bars**: Visual representation of cap usage
- **Player Lists**: Scrollable roster with salaries
- **Sorting**: Teams ordered by total salary

### **League Overview**
- **Total Teams**: Number of teams loaded
- **Total Players**: League-wide player count
- **Average Salary**: Mean team salary across league
- **Teams Over Cap**: Count of teams exceeding $110M

### **Data Management**
- **Refresh**: Update data from Google Sheets
- **Export**: Download complete league data as JSON
- **Responsive**: Works on all device sizes

## 🔧 **Configuration**

### **Google Sheets Setup**
```javascript
// In dynasty-league-ui.html
const SHEET_ID = '1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY';
const SALARY_CAP = 110; // $110M salary cap
```

### **Team Codes**
The system loads these NBA team sheets:
```
ATL, BOS, BRK, CHA, CHI, CLE, DAL, DEN, DET, GSW,
HOU, IND, LAC, LAL, MEM, MIA, MIL, MIN, NOP, NYK,
OKC, ORL, PHI, PHX, POR, SAC, SAS, TOR, UTA, WAS
```

### **Server Configuration**
```javascript
// In dynasty-server.js
const port = process.env.PORT || 3004;
```

## 📋 **API Endpoints**

### **Main Interface**
- `GET /` - Dynasty League dashboard
- `GET /api/health` - Health check

### **Data Access**
- `GET /api/sheets/:sheetId/:teamCode` - Fetch team data
- `GET /api/league/summary` - League statistics

## 🎨 **Customization**

### **Team Colors**
Update CSS for custom team colors:
```css
.team-header {
    background: linear-gradient(135deg, #your-color1, #your-color2);
}
```

### **Salary Cap Limits**
Modify salary cap per team:
```javascript
const teamCaps = {
    'LAL': 120, // Lakers get higher cap
    'GSW': 115, // Warriors get higher cap
    // ... other teams use default $110M
};
```

### **Contract Years**
Focus on different contract years:
```javascript
// Change from 2026 to 2027
if (year === '2027' && capStr && expensesStr) {
    // Parse 2027 salary data
}
```

## 🔍 **Troubleshooting**

### **Google Sheets Access**
- Ensure your sheet is **publicly viewable**
- Check that team sheet names match NBA codes
- Verify salary data format matches expected structure

### **Data Loading Issues**
- Check browser console for errors
- Verify sheet structure matches expected format
- Use demo data for testing if sheets are private

### **Performance**
- Loading 30 teams may take 10-15 seconds
- Large rosters (15+ players) load slower
- Consider caching for production use

## 🎉 **Demo Data**

If Google Sheets loading fails, demo data includes:
- **Los Angeles Lakers**: $135.3M (over cap)
- **Golden State Warriors**: $114.5M (over cap)
- Realistic NBA player salaries and positions

## 🚀 **Advanced Features**

### **Future Enhancements**
- **Multi-year Projections**: Contract year analysis
- **Trade Analyzer**: Salary cap impact calculator
- **Waiver Wire**: Free agent salary tracking
- **Draft Integration**: Rookie contract management

### **Data Export**
- **JSON Format**: Complete league data export
- **CSV Export**: Spreadsheet-compatible format
- **Backup System**: Regular data snapshots

## 💡 **Pro Tips**

### **Dynasty Management**
- Monitor teams approaching salary cap
- Track contract expiration years
- Plan for future salary increases
- Balance star players vs. depth

### **Data Accuracy**
- Keep Google Sheets updated regularly
- Use consistent salary formatting
- Verify player names and positions
- Check contract year accuracy

**🔗 Access your dynasty league:** http://localhost:3004

Your fantasy basketball dynasty league management just got a major upgrade! 🏀

## 📞 **Support**

- **Health Check**: http://localhost:3004/api/health
- **Google Sheets**: https://docs.google.com/spreadsheets/d/1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY/edit
- **Demo Mode**: Automatic fallback if sheets are inaccessible
