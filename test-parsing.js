#!/usr/bin/env node

// Test script to verify CSV parsing logic

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const SHEET_ID = '1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY';

// Parse CSV line handling quotes and commas
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    
    result.push(current.trim());
    return result.map(field => field.replace(/^"|"$/g, '')); // Remove surrounding quotes
}

// Parse CSV data for a team
function parseTeamCSV(csvText, teamCode) {
    const lines = csvText.split('\n').filter(line => line.trim());
    const players = [];
    const draftPicks = [];
    let totalSalary = 0;
    let salaryCapUsed = 0;
    let salaryCapLimit = 110;
    
    console.log(`🔍 Parsing ${teamCode} - ${lines.length} lines`);
    
    // Find the salary cap information first (look for 2026 row)
    for (let i = 0; i < Math.min(10, lines.length); i++) {
        const columns = parseCSVLine(lines[i]);
        if (columns.length >= 4) {
            const year = columns[0]?.trim();
            const capStr = columns[1]?.trim();
            const expensesStr = columns[2]?.trim();
            
            if (year === '2026' && capStr && expensesStr) {
                // Extract salary cap limit
                const capMatch = capStr.match(/\$(\d+)m/i);
                if (capMatch) {
                    salaryCapLimit = parseInt(capMatch[1]);
                }
                
                // Extract current expenses (salary used)
                const expensesMatch = expensesStr.match(/\$([0-9.]+)m/i);
                if (expensesMatch) {
                    salaryCapUsed = parseFloat(expensesMatch[1]);
                }
                console.log(`💰 ${teamCode} Cap: $${salaryCapLimit}M, Used: $${salaryCapUsed}M`);
                break;
            }
        }
    }
    
    // Find where the roster starts (look for "Position" header)
    let rosterStartIndex = -1;
    for (let i = 0; i < lines.length; i++) {
        const columns = parseCSVLine(lines[i]);
        if (columns.length >= 3) {
            const col0 = columns[0]?.trim().toLowerCase();
            const col2 = columns[2]?.trim().toLowerCase();
            
            if (col0 === 'position' && col2 === 'name') {
                rosterStartIndex = i + 1;
                console.log(`📋 Found roster header at line ${i + 1}`);
                break;
            }
        }
    }
    
    // Parse player data starting from roster section
    if (rosterStartIndex > 0) {
        for (let i = rosterStartIndex; i < lines.length; i++) {
            const columns = parseCSVLine(lines[i]);
            if (columns.length >= 6) {
                const position = columns[0]?.trim();
                const playerName = columns[2]?.trim();
                const salaryStr = columns[5]?.trim(); // 2026 salary column
                
                // Skip empty rows or invalid data
                if (!playerName || playerName === '' || playerName === 'Name') {
                    continue;
                }
                
                // Parse salary
                let salary = 0;
                if (salaryStr && salaryStr.includes('$')) {
                    const salaryMatch = salaryStr.match(/\$([0-9.]+)m/i);
                    if (salaryMatch) {
                        salary = parseFloat(salaryMatch[1]);
                    }
                }
                
                // Only add players with valid names
                if (playerName && playerName.length > 1) {
                    // Check if this is a draft pick
                    if (playerName.includes('1st') || playerName.includes('2nd') || playerName.includes('3rd')) {
                        draftPicks.push({
                            name: playerName,
                            position: position || 'Pick',
                            salary: salary,
                            year: playerName.match(/(\d{4})/) ? playerName.match(/(\d{4})/)[1] : '2025',
                            round: playerName.includes('1st') ? '1st Round' : playerName.includes('2nd') ? '2nd Round' : '3rd Round'
                        });
                        console.log(`🎯 ${playerName} (Draft Pick) - $${salary}M`);
                    } else {
                        players.push({
                            name: playerName,
                            position: position || 'Unknown',
                            salary: salary
                        });
                        console.log(`👤 ${playerName} (${position}) - $${salary}M`);
                    }
                    totalSalary += salary;
                }
            }
        }
    }
    
    // Parse contract adjustments section
    const contractAdjustments = {};
    let adjustmentStartIndex = -1;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        if (line.includes('cap adjustments')) {
            adjustmentStartIndex = i + 2; // Skip header row
            break;
        }
    }

    if (adjustmentStartIndex > 0) {
        for (let i = adjustmentStartIndex; i < lines.length; i++) {
            const columns = parseCSVLine(lines[i]);
            if (columns.length >= 6) {
                const playerName = columns[0]?.trim();
                const adjustment2026 = columns[5]?.trim();

                if (playerName && playerName !== '' && playerName !== 'Player' && adjustment2026) {
                    const adjustmentMatch = adjustment2026.match(/([+-]?\$?[0-9.]+)m/i);
                    if (adjustmentMatch) {
                        const adjustmentValue = parseFloat(adjustmentMatch[1].replace('$', ''));
                        contractAdjustments[playerName] = adjustmentValue;
                        console.log(`💰 ${playerName} adjustment: ${adjustmentValue > 0 ? '+' : ''}$${adjustmentValue}M`);
                        totalSalary += adjustmentValue;
                        salaryCapUsed += adjustmentValue;
                    }
                }
            }
        }
    }

    console.log(`✅ ${teamCode}: ${players.length} players, ${draftPicks.length} picks, $${totalSalary.toFixed(1)}M total`);

    return {
        code: teamCode,
        players: players,
        draftPicks: draftPicks,
        contractAdjustments: contractAdjustments,
        totalSalary: totalSalary,
        salaryCapUsed: salaryCapUsed,
        salaryCapLimit: salaryCapLimit
    };
}

async function testTeamParsing(teamCode) {
    try {
        console.log(`\n🧪 Testing ${teamCode} parsing...`);
        console.log('='.repeat(50));
        
        const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/gviz/tq?tqx=out:csv&sheet=${teamCode}`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const csvText = await response.text();
        const teamData = parseTeamCSV(csvText, teamCode);
        
        console.log(`\n📊 Summary for ${teamCode}:`);
        console.log(`   Players: ${teamData.players.length}`);
        console.log(`   Draft Picks: ${teamData.draftPicks.length}`);
        console.log(`   Total Salary: $${teamData.totalSalary.toFixed(1)}M`);
        console.log(`   Cap Used: $${teamData.salaryCapUsed}M`);
        console.log(`   Cap Limit: $${teamData.salaryCapLimit}M`);
        console.log(`   Cap Space: $${(teamData.salaryCapLimit - teamData.salaryCapUsed).toFixed(1)}M`);
        
        return teamData;
    } catch (error) {
        console.error(`❌ Error testing ${teamCode}:`, error.message);
        return null;
    }
}

async function runTests() {
    console.log('🏀 Dynasty League CSV Parsing Test');
    console.log('==================================\n');
    
    // Test a few teams
    const testTeams = ['TOR', 'LAL', 'GSW'];
    
    for (const team of testTeams) {
        await testTeamParsing(team);
    }
    
    console.log('\n🎉 Testing complete!');
}

if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { parseTeamCSV, parseCSVLine };
