#!/usr/bin/env node

const express = require('express');
const path = require('path');

const app = express();
const port = process.env.PORT || 3004;

// Middleware
app.use(express.json());
app.use(express.static('.'));

// CORS headers for all routes
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  next();
});

// Serve the main UI
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'dynasty-league-ui.html'));
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'Dynasty League UI'
  });
});

// Proxy endpoint for Google Sheets (if needed)
app.get('/api/sheets/:sheetId/:teamCode', async (req, res) => {
  try {
    const { sheetId, teamCode } = req.params;
    const url = `https://docs.google.com/spreadsheets/d/${sheetId}/gviz/tq?tqx=out:csv&sheet=${teamCode}`;
    
    console.log(`📊 Fetching ${teamCode} data: ${url}`);
    
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const csvData = await response.text();
    
    // Check if we got HTML instead of CSV (indicates auth error)
    if (csvData.includes('<HTML>') || csvData.includes('<!DOCTYPE')) {
      throw new Error('Google Sheet is private or requires authentication');
    }
    
    res.setHeader('Content-Type', 'text/csv');
    res.send(csvData);
  } catch (error) {
    console.error(`❌ Error fetching ${req.params.teamCode}:`, error.message);
    
    if (error.message.includes('private') || error.message.includes('authentication')) {
      res.status(403).json({ 
        error: 'Google Sheet access denied',
        message: 'The sheet is private. Please make it publicly accessible.',
        help: 'Share the sheet with "Anyone with the link" and "Viewer" permissions'
      });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

// Get league summary
app.get('/api/league/summary', (req, res) => {
  // This would contain cached league data in a real implementation
  res.json({
    totalTeams: 30,
    totalPlayers: 450,
    avgSalary: 95.2,
    teamsOverCap: 18,
    lastUpdated: new Date().toISOString()
  });
});

// Start server
app.listen(port, () => {
  console.log('🏀 Dynasty Basketball League UI');
  console.log('===============================');
  console.log(`🚀 Server running on port ${port}`);
  console.log(`📱 Dashboard: http://localhost:${port}`);
  console.log(`📊 Google Sheets: https://docs.google.com/spreadsheets/d/1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY/edit`);
  console.log(`🔗 Health check: http://localhost:${port}/api/health`);
  console.log('');
  console.log('Features:');
  console.log('• 📊 Load all 30 team rosters from Google Sheets');
  console.log('• 💰 Visual salary cap management');
  console.log('• 📈 League overview statistics');
  console.log('• 🎯 Team-by-team salary breakdowns');
  console.log('• 📱 Responsive design for all devices');
  console.log('• 📤 Export league data');
  console.log('');
});

module.exports = app;
