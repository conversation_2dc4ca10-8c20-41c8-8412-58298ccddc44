#!/usr/bin/env node

// Test script to check all 30 teams

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const SHEET_ID = '1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY';

const teamCodes = [
    'ATL', 'BOS', 'BRK', 'CHA', 'CHI', 'CLE', 'DAL', 'DEN', 'DET', 'GSW',
    'HOU', 'IND', 'LAC', 'LAL', 'MEM', 'MIA', 'MIL', 'MIN', 'NOP', 'NYK',
    'OKC', 'ORL', 'PHI', 'PHX', 'POR', 'SAC', 'SAS', 'TOR', 'UTA', 'WAS'
];

async function testTeamAccess(teamCode) {
    try {
        const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/gviz/tq?tqx=out:csv&sheet=${teamCode}`;
        const response = await fetch(url);
        
        if (!response.ok) {
            return { teamCode, status: 'ERROR', error: `HTTP ${response.status}` };
        }
        
        const csvText = await response.text();
        const lines = csvText.split('\n').filter(line => line.trim());
        
        // Check if it's a valid sheet (has some content)
        if (lines.length < 5) {
            return { teamCode, status: 'EMPTY', lines: lines.length };
        }
        
        // Check if it has salary cap info
        const hasSalaryCap = lines.some(line => line.includes('2026') && line.includes('$'));
        
        // Check if it has roster
        const hasRoster = lines.some(line => line.toLowerCase().includes('position'));
        
        return { 
            teamCode, 
            status: 'SUCCESS', 
            lines: lines.length,
            hasSalaryCap,
            hasRoster
        };
        
    } catch (error) {
        return { teamCode, status: 'ERROR', error: error.message };
    }
}

async function testAllTeams() {
    console.log('🏀 Testing All 30 NBA Teams');
    console.log('============================\n');
    
    const results = [];
    let successCount = 0;
    let errorCount = 0;
    let emptyCount = 0;
    
    for (const teamCode of teamCodes) {
        const result = await testTeamAccess(teamCode);
        results.push(result);
        
        if (result.status === 'SUCCESS') {
            successCount++;
            console.log(`✅ ${teamCode}: ${result.lines} lines, Cap: ${result.hasSalaryCap ? 'Yes' : 'No'}, Roster: ${result.hasRoster ? 'Yes' : 'No'}`);
        } else if (result.status === 'EMPTY') {
            emptyCount++;
            console.log(`⚠️ ${teamCode}: Empty sheet (${result.lines} lines)`);
        } else {
            errorCount++;
            console.log(`❌ ${teamCode}: ${result.error}`);
        }
    }
    
    console.log('\n📊 Summary:');
    console.log(`Total Teams: ${teamCodes.length}`);
    console.log(`✅ Accessible: ${successCount}`);
    console.log(`⚠️ Empty: ${emptyCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    
    if (successCount < 30) {
        console.log('\n🔍 Teams with issues:');
        results.filter(r => r.status !== 'SUCCESS').forEach(r => {
            console.log(`   ${r.teamCode}: ${r.status} - ${r.error || 'Empty sheet'}`);
        });
    }
    
    console.log(`\n🎯 Expected to load: ${successCount} teams`);
}

if (require.main === module) {
    testAllTeams().catch(console.error);
}

module.exports = { testAllTeams };
