<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏀 Dynasty Basketball Salary League</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
            line-height: 1.6;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 16px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo h1 {
            font-size: 1.5em;
            font-weight: 600;
            color: #1e293b;
        }

        .controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        .btn-secondary {
            background: white;
            color: #64748b;
            border-color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f8fafc;
            color: #475569;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px 20px;
        }

        .status {
            background: white;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            display: none;
        }

        .status.loading {
            border-left: 4px solid #3b82f6;
            color: #1e40af;
        }

        .status.success {
            border-left: 4px solid #10b981;
            color: #047857;
        }

        .status.error {
            border-left: 4px solid #ef4444;
            color: #dc2626;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 0.875rem;
        }

        .teams-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 16px;
        }

        .team-card {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            overflow: hidden;
        }

        .team-card:hover {
            border-color: #3b82f6;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .team-card.clickable {
            cursor: pointer;
        }

        .team-header {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
        }

        .team-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .salary-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .salary-amount {
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
        }

        .salary-status {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .over-cap {
            background: #fef2f2;
            color: #dc2626;
        }

        .under-cap {
            background: #f0fdf4;
            color: #16a34a;
        }

        .salary-bar {
            width: 100%;
            height: 4px;
            background: #f1f5f9;
            border-radius: 2px;
            overflow: hidden;
        }

        .salary-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .players-section {
            padding: 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #374151;
        }

        .player-count {
            background: #f3f4f6;
            color: #6b7280;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .players-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .player-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .player-item:last-child {
            border-bottom: none;
        }

        .player-info {
            flex: 1;
        }

        .player-name {
            font-weight: 600;
            color: #111827;
            margin-bottom: 2px;
        }

        .player-position {
            font-size: 0.8em;
            color: #6b7280;
        }

        .player-salary {
            font-weight: 700;
            color: #059669;
            font-size: 0.95em;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        .empty-state h3 {
            margin-bottom: 8px;
            color: #374151;
        }

        .team-view {
            display: none;
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .team-view.active {
            display: block;
        }

        .team-view-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f3f4f6;
        }

        .team-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .team-title h2 {
            font-size: 2em;
            color: #111827;
            margin: 0;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #4b5563;
            transform: translateY(-1px);
        }

        .team-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .players-section-detailed {
            background: #f9fafb;
            border-radius: 12px;
            padding: 20px;
        }

        .draft-picks-section {
            background: #fef3c7;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #f59e0b;
        }

        .section-title-large {
            font-size: 1.3em;
            font-weight: 700;
            color: #111827;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .player-item-detailed {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 15px;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .player-item-detailed:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .draft-pick-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            border-left: 4px solid #f59e0b;
        }

        .pick-info {
            flex: 1;
        }

        .pick-year {
            font-weight: 600;
            color: #92400e;
        }

        .pick-details {
            font-size: 0.9em;
            color: #78716c;
        }

        .salary-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .breakdown-card {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .breakdown-value {
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .breakdown-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* Trade Machine Styles */
        .trade-machine {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .trade-machine.active {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .trade-machine-content {
            background: white;
            border-radius: 8px;
            width: 100%;
            max-width: 1400px;
            max-height: 90vh;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .trade-machine-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .trade-machine-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .close-trade-machine {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            color: #64748b;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .close-trade-machine:hover {
            background: #f1f5f9;
            color: #475569;
        }

        .trade-teams-container {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 24px;
            padding: 24px;
            flex: 1;
            overflow: hidden;
        }

        .trade-team {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .trade-team.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .trade-team-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
        }

        .trade-team-selector {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-weight: 600;
            cursor: pointer;
        }

        .trade-team-name {
            font-size: 1.2em;
            font-weight: 700;
            color: #111827;
        }

        .trade-salary-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #3b82f6;
        }

        .trade-salary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .trade-salary-row:last-child {
            margin-bottom: 0;
            font-weight: 700;
            padding-top: 5px;
            border-top: 1px solid #e5e7eb;
        }

        .trade-players-list {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
            max-height: 400px;
        }

        .trade-player-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: grab;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .trade-player-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
            transform: translateY(-1px);
        }

        .trade-player-item:active {
            cursor: grabbing;
        }

        .trade-player-item.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
        }

        .trade-player-info {
            flex: 1;
        }

        .trade-player-name {
            font-weight: 600;
            color: #111827;
            margin-bottom: 2px;
        }

        .trade-player-position {
            font-size: 0.8em;
            color: #6b7280;
        }

        .trade-player-salary {
            font-weight: 700;
            color: #059669;
            font-size: 1em;
        }

        .trade-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .trade-arrow-icon {
            font-size: 2em;
            color: #6b7280;
            background: white;
            padding: 15px;
            border-radius: 50%;
            border: 2px solid #e5e7eb;
        }

        .trade-summary {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin: 0 24px;
            flex-shrink: 0;
        }

        .trade-summary-title {
            font-size: 1.2em;
            font-weight: 700;
            color: #0c4a6e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .trade-impact {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .trade-impact-team {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #0ea5e9;
        }

        .trade-impact-team h4 {
            margin-bottom: 10px;
            color: #0c4a6e;
        }

        .trade-impact-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .trade-impact-positive {
            color: #059669;
            font-weight: 600;
        }

        .trade-impact-negative {
            color: #dc2626;
            font-weight: 600;
        }

        .trade-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
            flex-shrink: 0;
        }

        .btn-trade {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-execute {
            background: #059669;
            color: white;
        }

        .btn-execute:hover {
            background: #047857;
            transform: translateY(-1px);
        }

        .btn-reset {
            background: #6b7280;
            color: white;
        }

        .btn-reset:hover {
            background: #4b5563;
        }

        .drop-zone-empty {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            margin-top: 20px;
        }

        .drop-zone-empty.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
            color: #3b82f6;
        }

        @media (max-width: 768px) {
            .teams-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <span style="font-size: 2em;">🏀</span>
                <h1>Dynasty Basketball League</h1>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="loadLeagueData()">
                    <span id="load-icon">📊</span>
                    <span id="load-text">Load League Data</span>
                </button>
                <button class="btn btn-secondary" onclick="refreshData()">
                    🔄 Refresh
                </button>
                <button class="btn btn-secondary" onclick="exportData()">
                    📤 Export
                </button>
                <button class="btn btn-primary" onclick="openTradeMachine()">
                    🔄 Trade Machine
                </button>
            </div>
        </div>
    </div>

    <div class="container">
        <div id="status" class="status"></div>

        <div class="page-header">
            <h1 class="page-title">Dynasty Teams</h1>
            <p class="page-subtitle">Click any team to view detailed roster and salary information</p>
        </div>

        <div class="teams-grid" id="teams-container">
            <!-- Teams will be loaded here -->
        </div>

        <!-- Individual Team View -->
        <div class="team-view" id="team-view">
            <div class="team-view-header">
                <div class="team-title">
                    <h2 id="team-view-name">Team Name</h2>
                    <span id="team-view-emoji" style="font-size: 1.5em;">🏀</span>
                </div>
                <button class="back-btn" onclick="showLeagueView()">
                    ← Back to League
                </button>
            </div>

            <div class="salary-breakdown" id="salary-breakdown">
                <!-- Salary breakdown cards will be inserted here -->
            </div>

            <div class="team-sections">
                <div class="players-section-detailed">
                    <div class="section-title-large">
                        👥 Active Roster
                        <span id="active-player-count" class="player-count">0 players</span>
                    </div>
                    <div id="active-players-list">
                        <!-- Active players will be loaded here -->
                    </div>
                </div>

                <div class="draft-picks-section">
                    <div class="section-title-large">
                        🎯 Draft Picks
                        <span id="draft-pick-count" class="player-count">0 picks</span>
                    </div>
                    <div id="draft-picks-list">
                        <!-- Draft picks will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Trade Machine Modal -->
        <div class="trade-machine" id="trade-machine">
            <div class="trade-machine-content">
                <div class="trade-machine-header">
                    <div class="trade-machine-title">
                        🔄 Dynasty Trade Machine
                    </div>
                    <button class="close-trade-machine" onclick="closeTradeMachine()">
                        ✕ Close
                    </button>
                </div>

                <div class="trade-teams-container">
                    <!-- Team A -->
                    <div class="trade-team" id="trade-team-a" ondrop="dropPlayerInTrade(event, 'A')" ondragover="allowTradeDrop(event)">
                        <div class="trade-team-header">
                            <div class="trade-team-name" id="trade-team-a-name">Select Team A</div>
                            <select class="trade-team-selector" id="trade-team-a-selector" onchange="selectTradeTeam('A', this.value)">
                                <option value="">Choose Team...</option>
                            </select>
                        </div>

                        <div class="trade-salary-info" id="trade-team-a-salary">
                            <div class="trade-salary-row">
                                <span>Current Salary:</span>
                                <span id="trade-team-a-current">$0.0M</span>
                            </div>
                            <div class="trade-salary-row">
                                <span>After Trade:</span>
                                <span id="trade-team-a-after">$0.0M</span>
                            </div>
                            <div class="trade-salary-row">
                                <span>Cap Space:</span>
                                <span id="trade-team-a-space">$0.0M</span>
                            </div>
                        </div>

                        <div class="trade-players-list" id="trade-team-a-players">
                            <div class="drop-zone-empty">
                                Select a team to see available players
                            </div>
                        </div>
                    </div>

                    <!-- Trade Arrow -->
                    <div class="trade-arrow">
                        <div class="trade-arrow-icon">⇄</div>
                    </div>

                    <!-- Team B -->
                    <div class="trade-team" id="trade-team-b" ondrop="dropPlayerInTrade(event, 'B')" ondragover="allowTradeDrop(event)">
                        <div class="trade-team-header">
                            <div class="trade-team-name" id="trade-team-b-name">Select Team B</div>
                            <select class="trade-team-selector" id="trade-team-b-selector" onchange="selectTradeTeam('B', this.value)">
                                <option value="">Choose Team...</option>
                            </select>
                        </div>

                        <div class="trade-salary-info" id="trade-team-b-salary">
                            <div class="trade-salary-row">
                                <span>Current Salary:</span>
                                <span id="trade-team-b-current">$0.0M</span>
                            </div>
                            <div class="trade-salary-row">
                                <span>After Trade:</span>
                                <span id="trade-team-b-after">$0.0M</span>
                            </div>
                            <div class="trade-salary-row">
                                <span>Cap Space:</span>
                                <span id="trade-team-b-space">$0.0M</span>
                            </div>
                        </div>

                        <div class="trade-players-list" id="trade-team-b-players">
                            <div class="drop-zone-empty">
                                Select a team to see available players
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trade Summary -->
                <div class="trade-summary" id="trade-summary" style="display: none;">
                    <div class="trade-summary-title">
                        📊 Trade Impact Analysis
                    </div>
                    <div class="trade-impact" id="trade-impact">
                        <!-- Trade impact will be calculated here -->
                    </div>
                </div>

                <!-- Trade Actions -->
                <div class="trade-actions">
                    <button class="btn-trade btn-reset" onclick="resetTrade()">
                        🔄 Reset Trade
                    </button>
                    <button class="btn-trade btn-execute" onclick="executeTrade()" id="execute-trade-btn" disabled>
                        ✅ Execute Trade
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const SHEET_ID = '1aJJygcW2Bo64iT7SpyuF6bTXLzKgFZdxGc3skG0WJWY';
        const SALARY_CAP = 110; // $110M salary cap
        
        // Global state
        let leagueData = {};
        let isLoading = false;
        let tradeState = {
            teamA: null,
            teamB: null,
            playersA: [], // Players going to team A
            playersB: [], // Players going to team B
            originalTeamA: null,
            originalTeamB: null
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏀 Dynasty League UI initialized');
            loadLeagueData();
        });

        // Show status message
        function showStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 4000);
            }
        }

        // Update loading button state
        function setLoadingState(loading) {
            isLoading = loading;
            const icon = document.getElementById('load-icon');
            const text = document.getElementById('load-text');
            
            if (loading) {
                icon.innerHTML = '<div class="loading-spinner"></div>';
                text.textContent = 'Loading...';
            } else {
                icon.textContent = '📊';
                text.textContent = 'Load League Data';
            }
        }

        // Load league data from Google Sheets
        async function loadLeagueData() {
            if (isLoading) return;
            
            setLoadingState(true);
            showStatus('📊 Loading dynasty league data from Google Sheets...', 'loading');
            
            try {
                // Get list of team sheets
                const teamCodes = [
                    'ATL', 'BOS', 'BRK', 'CHA', 'CHI', 'CLE', 'DAL', 'DEN', 'DET', 'GSW',
                    'HOU', 'IND', 'LAC', 'LAL', 'MEM', 'MIA', 'MIL', 'MIN', 'NOP', 'NYK',
                    'OKC', 'ORL', 'PHI', 'PHX', 'POR', 'SAC', 'SAS', 'TOR', 'UTA', 'WAS'
                ];
                
                leagueData = {};
                let successCount = 0;
                
                // Load each team's data
                console.log(`🔄 Loading ${teamCodes.length} teams...`);
                for (const teamCode of teamCodes) {
                    try {
                        const teamData = await loadTeamData(teamCode);
                        if (teamData) {
                            // Always load the team, even if it has no players (might have salary cap info)
                            leagueData[teamCode] = teamData;
                            successCount++;
                            console.log(`✅ Loaded ${teamCode}: ${teamData.players.length} players, ${teamData.draftPicks ? teamData.draftPicks.length : 0} picks, $${teamData.salaryCapUsed}M used`);
                        } else {
                            console.log(`⚠️ ${teamCode}: No data returned`);
                        }
                    } catch (error) {
                        console.warn(`❌ Could not load ${teamCode}:`, error.message);
                    }
                }
                
                if (successCount > 0) {
                    renderLeague();
                    showStatus(`✅ Loaded ${successCount} teams successfully!`, 'success');
                } else {
                    throw new Error('No team data could be loaded. Sheet may be private.');
                }
                
            } catch (error) {
                console.error('❌ Error loading league data:', error);
                showStatus(`❌ ${error.message}`, 'error');
                loadDemoData();
            } finally {
                setLoadingState(false);
            }
        }

        // Load individual team data
        async function loadTeamData(teamCode) {
            const url = `https://docs.google.com/spreadsheets/d/${SHEET_ID}/gviz/tq?tqx=out:csv&sheet=${teamCode}`;

            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const csvText = await response.text();
                const teamData = parseTeamCSV(csvText, teamCode);

                // Always return team data, even if empty
                if (!teamData) {
                    return {
                        code: teamCode,
                        name: getTeamName(teamCode),
                        players: [],
                        draftPicks: [],
                        contractAdjustments: {},
                        totalSalary: 0,
                        salaryCapUsed: 0,
                        salaryCapLimit: 110
                    };
                }

                return teamData;
            } catch (error) {
                console.warn(`Could not load ${teamCode}:`, error.message);
                // Return empty team data instead of failing
                return {
                    code: teamCode,
                    name: getTeamName(teamCode),
                    players: [],
                    draftPicks: [],
                    contractAdjustments: {},
                    totalSalary: 0,
                    salaryCapUsed: 0,
                    salaryCapLimit: 110
                };
            }
        }

        // Parse CSV data for a team
        function parseTeamCSV(csvText, teamCode) {
            const lines = csvText.split('\n').filter(line => line.trim());
            const players = [];
            const draftPicks = [];
            const contractAdjustments = {};
            let totalSalary = 0;
            let salaryCapUsed = 0;
            let salaryCapLimit = 110; // Default from the sheets

            console.log(`🔍 Parsing ${teamCode} - ${lines.length} lines`);

            // Find the salary cap information first (look for 2026 row)
            for (let i = 0; i < Math.min(10, lines.length); i++) {
                const columns = parseCSVLine(lines[i]);
                if (columns.length >= 4) {
                    const year = columns[0]?.trim().replace(/"/g, '');
                    const capStr = columns[1]?.trim().replace(/"/g, '');
                    const expensesStr = columns[2]?.trim().replace(/"/g, '');

                    if (year === '2026' && capStr && expensesStr) {
                        // Extract salary cap limit
                        const capMatch = capStr.match(/\$(\d+)m/i);
                        if (capMatch) {
                            salaryCapLimit = parseInt(capMatch[1]);
                        }

                        // Extract current expenses (salary used)
                        const expensesMatch = expensesStr.match(/\$([0-9.]+)m/i);
                        if (expensesMatch) {
                            salaryCapUsed = parseFloat(expensesMatch[1]);
                        }
                        console.log(`💰 ${teamCode} Cap: $${salaryCapLimit}M, Used: $${salaryCapUsed}M`);
                        break;
                    }
                }
            }

            // Find where the roster starts (look for "Position" header)
            let rosterStartIndex = -1;
            for (let i = 0; i < lines.length; i++) {
                const columns = parseCSVLine(lines[i]);
                if (columns.length >= 3) {
                    const col0 = columns[0]?.trim().replace(/"/g, '').toLowerCase();
                    const col2 = columns[2]?.trim().replace(/"/g, '').toLowerCase();

                    if (col0 === 'position' && col2 === 'name') {
                        rosterStartIndex = i + 1; // Start after the header
                        console.log(`📋 Found roster header at line ${i + 1}`);
                        break;
                    }
                }
            }

            // Parse player data starting from roster section
            if (rosterStartIndex > 0) {
                for (let i = rosterStartIndex; i < lines.length; i++) {
                    const columns = parseCSVLine(lines[i]);
                    if (columns.length >= 6) {
                        const position = columns[0]?.trim().replace(/"/g, '');
                        const playerName = columns[2]?.trim().replace(/"/g, '');
                        const salaryStr = columns[5]?.trim().replace(/"/g, ''); // 2026 salary column

                        // Skip empty rows or invalid data
                        if (!playerName || playerName === '' || playerName === 'Name') {
                            continue;
                        }

                        // Parse salary
                        let salary = 0;
                        if (salaryStr && salaryStr.includes('$')) {
                            const salaryMatch = salaryStr.match(/\$([0-9.]+)m/i);
                            if (salaryMatch) {
                                salary = parseFloat(salaryMatch[1]);
                            }
                        }

                        // Only add players with valid names
                        if (playerName && playerName.length > 1) {
                            // Check if this is a draft pick
                            if (playerName.includes('1st') || playerName.includes('2nd') || playerName.includes('3rd')) {
                                draftPicks.push({
                                    id: `${teamCode}_pick_${i}`,
                                    name: playerName,
                                    position: position || 'Pick',
                                    salary: salary,
                                    team: teamCode,
                                    year: extractPickYear(playerName),
                                    round: extractPickRound(playerName),
                                    details: extractPickDetails(playerName)
                                });
                                console.log(`🎯 ${playerName} (Draft Pick) - $${salary}M`);
                            } else {
                                // Regular player
                                players.push({
                                    id: `${teamCode}_${i}`,
                                    name: playerName,
                                    position: position || 'Unknown',
                                    salary: salary,
                                    team: teamCode
                                });
                                console.log(`👤 ${playerName} (${position}) - $${salary}M`);
                            }
                            totalSalary += salary;
                        }
                    }
                }
            }

            // Parse contract adjustments section
            let adjustmentStartIndex = -1;
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].toLowerCase();
                if (line.includes('cap adjustments')) {
                    adjustmentStartIndex = i + 2; // Skip header row
                    break;
                }
            }

            if (adjustmentStartIndex > 0) {
                for (let i = adjustmentStartIndex; i < lines.length; i++) {
                    const columns = parseCSVLine(lines[i]);
                    if (columns.length >= 6) {
                        const playerName = columns[0]?.trim();
                        const adjustment2026 = columns[5]?.trim();

                        if (playerName && playerName !== '' && playerName !== 'Player' && adjustment2026) {
                            const adjustmentMatch = adjustment2026.match(/([+-]?\$?[0-9.]+)m/i);
                            if (adjustmentMatch) {
                                const adjustmentValue = parseFloat(adjustmentMatch[1].replace('$', ''));
                                contractAdjustments[playerName] = adjustmentValue;
                                console.log(`💰 ${playerName} adjustment: ${adjustmentValue > 0 ? '+' : ''}$${adjustmentValue}M`);
                            }
                        }
                    }
                }
            }

            // Apply contract adjustments to players
            players.forEach(player => {
                if (contractAdjustments[player.name]) {
                    player.adjustment = contractAdjustments[player.name];
                    player.adjustedSalary = player.salary + player.adjustment;
                    // Update totals with adjustment
                    totalSalary += player.adjustment;
                    salaryCapUsed += player.adjustment;
                }
            });

            console.log(`✅ ${teamCode}: ${players.length} players, ${draftPicks.length} picks, $${totalSalary.toFixed(1)}M total`);

            // If no data found but sheet exists, still return team structure
            if (players.length === 0 && draftPicks.length === 0 && salaryCapUsed === 0) {
                console.log(`📋 ${teamCode}: Empty sheet, creating placeholder team`);
            }

            return {
                code: teamCode,
                name: getTeamName(teamCode),
                players: players,
                draftPicks: draftPicks,
                contractAdjustments: contractAdjustments,
                totalSalary: totalSalary,
                salaryCapUsed: salaryCapUsed || totalSalary, // Use parsed value or calculated
                salaryCapLimit: 110 // Always use $110M cap
            };
        }

        // Helper functions for draft pick parsing
        function extractPickYear(pickName) {
            const yearMatch = pickName.match(/(\d{4})/);
            return yearMatch ? yearMatch[1] : '2025';
        }

        function extractPickRound(pickName) {
            if (pickName.includes('1st')) return '1st Round';
            if (pickName.includes('2nd')) return '2nd Round';
            if (pickName.includes('3rd')) return '3rd Round';
            return 'Unknown Round';
        }

        function extractPickDetails(pickName) {
            // Extract details like "1.1" or "2.14"
            const detailMatch = pickName.match(/(\d+\.\d+)/);
            return detailMatch ? `Pick ${detailMatch[1]}` : '';
        }

        // Parse CSV line handling quotes and commas
        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];

                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }

            result.push(current.trim());
            return result.map(field => field.replace(/^"|"$/g, '')); // Remove surrounding quotes
        }

        // Get full team name from code
        function getTeamName(code) {
            const teamNames = {
                'ATL': 'Atlanta Hawks', 'BOS': 'Boston Celtics', 'BRK': 'Brooklyn Nets',
                'CHA': 'Charlotte Hornets', 'CHI': 'Chicago Bulls', 'CLE': 'Cleveland Cavaliers',
                'DAL': 'Dallas Mavericks', 'DEN': 'Denver Nuggets', 'DET': 'Detroit Pistons',
                'GSW': 'Golden State Warriors', 'HOU': 'Houston Rockets', 'IND': 'Indiana Pacers',
                'LAC': 'LA Clippers', 'LAL': 'Los Angeles Lakers', 'MEM': 'Memphis Grizzlies',
                'MIA': 'Miami Heat', 'MIL': 'Milwaukee Bucks', 'MIN': 'Minnesota Timberwolves',
                'NOP': 'New Orleans Pelicans', 'NYK': 'New York Knicks', 'OKC': 'Oklahoma City Thunder',
                'ORL': 'Orlando Magic', 'PHI': 'Philadelphia 76ers', 'PHX': 'Phoenix Suns',
                'POR': 'Portland Trail Blazers', 'SAC': 'Sacramento Kings', 'SAS': 'San Antonio Spurs',
                'TOR': 'Toronto Raptors', 'UTA': 'Utah Jazz', 'WAS': 'Washington Wizards'
            };
            return teamNames[code] || code;
        }

        // Render the entire league
        function renderLeague() {
            renderTeams();
        }

        // Render all teams
        function renderTeams() {
            const container = document.getElementById('teams-container');
            container.innerHTML = '';

            if (Object.keys(leagueData).length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>No team data loaded</h3>
                        <p>Click "Load League Data" to fetch team rosters from Google Sheets</p>
                    </div>
                `;
                return;
            }

            // Sort teams by total salary (highest first)
            const sortedTeams = Object.values(leagueData)
                .sort((a, b) => b.totalSalary - a.totalSalary);

            sortedTeams.forEach(team => {
                const teamCard = createTeamCard(team);
                container.appendChild(teamCard);
            });
        }

        // Create team card element
        function createTeamCard(team) {
            const card = document.createElement('div');
            card.className = 'team-card clickable';
            card.onclick = () => showTeamView(team.code);

            const salaryPercentage = (team.salaryCapUsed / team.salaryCapLimit) * 100;
            const isOverCap = team.salaryCapUsed > team.salaryCapLimit;
            const capSpace = team.salaryCapLimit - team.salaryCapUsed;
            const totalAssets = team.players.length + (team.draftPicks ? team.draftPicks.length : 0);

            card.innerHTML = `
                <div class="team-header">
                    <div class="team-name">${team.name}</div>
                    <div class="salary-summary">
                        <div class="salary-amount">$${team.salaryCapUsed.toFixed(1)}M</div>
                        <div class="salary-status ${isOverCap ? 'over-cap' : 'under-cap'}">
                            ${isOverCap ? `$${Math.abs(capSpace).toFixed(1)}M over` : `$${capSpace.toFixed(1)}M under`}
                        </div>
                    </div>
                    <div class="salary-bar">
                        <div class="salary-fill" style="width: ${Math.min(salaryPercentage, 100)}%"></div>
                    </div>
                </div>
                <div class="players-section">
                    <div class="section-header">
                        <div class="section-title">Assets</div>
                        <div class="player-count">${team.players.length} players, ${team.draftPicks ? team.draftPicks.length : 0} picks</div>
                    </div>
                    <div class="players-list">
                        ${team.players
                            .sort((a, b) => (b.adjustedSalary || b.salary) - (a.adjustedSalary || a.salary))
                            .slice(0, 8)
                            .map(player => `
                                <div class="player-item">
                                    <div class="player-info">
                                        <div class="player-name">${player.name}</div>
                                        <div class="player-position">${player.position}</div>
                                    </div>
                                    <div class="player-salary">
                                        $${(player.adjustedSalary || player.salary).toFixed(1)}M
                                        ${player.adjustment ? `<span style="font-size: 0.75rem; color: #64748b;">(${player.adjustment > 0 ? '+' : ''}${player.adjustment.toFixed(1)})</span>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        ${team.players.length > 8 ? `<div style="text-align: center; padding: 8px; color: #64748b; font-size: 0.875rem;">+${team.players.length - 8} more players</div>` : ''}
                    </div>
                </div>
            `;

            return card;
        }

        // Load demo data as fallback
        function loadDemoData() {
            console.log('📊 Loading demo data...');

            leagueData = {
                'LAL': {
                    code: 'LAL',
                    name: 'Los Angeles Lakers',
                    players: [
                        { id: 'lal_1', name: 'LeBron James', position: 'SF', salary: 47.6, team: 'LAL' },
                        { id: 'lal_2', name: 'Anthony Davis', position: 'PF', salary: 40.6, team: 'LAL' },
                        { id: 'lal_3', name: 'Russell Westbrook', position: 'PG', salary: 47.1, team: 'LAL' }
                    ],
                    draftPicks: [
                        { id: 'lal_pick_1', name: '2025 LAL 1st', year: '2025', round: '1st Round', details: 'Pick 1.8', salary: 0, team: 'LAL' },
                        { id: 'lal_pick_2', name: '2026 LAL 2nd', year: '2026', round: '2nd Round', details: 'Pick 2.8', salary: 0, team: 'LAL' }
                    ],
                    totalSalary: 135.3,
                    salaryCapUsed: 135.3,
                    salaryCapLimit: 110
                },
                'GSW': {
                    code: 'GSW',
                    name: 'Golden State Warriors',
                    players: [
                        { id: 'gsw_1', name: 'Stephen Curry', position: 'PG', salary: 48.1, team: 'GSW' },
                        { id: 'gsw_2', name: 'Klay Thompson', position: 'SG', salary: 40.6, team: 'GSW' },
                        { id: 'gsw_3', name: 'Draymond Green', position: 'PF', salary: 25.8, team: 'GSW' }
                    ],
                    draftPicks: [
                        { id: 'gsw_pick_1', name: '2025 GSW 1st', year: '2025', round: '1st Round', details: 'Pick 1.30', salary: 0, team: 'GSW' }
                    ],
                    totalSalary: 114.5,
                    salaryCapUsed: 114.5,
                    salaryCapLimit: 110
                }
            };

            renderLeague();
            showStatus('📊 Demo data loaded - Make your Google Sheet public to load real data', 'success');
        }

        // Refresh data
        function refreshData() {
            loadLeagueData();
        }

        // Export data
        function exportData() {
            const dataStr = JSON.stringify(leagueData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `dynasty-league-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showStatus('📤 League data exported successfully!', 'success');
        }

        // Show individual team view
        function showTeamView(teamCode) {
            const team = leagueData[teamCode];
            if (!team) {
                showStatus('❌ Team not found', 'error');
                return;
            }

            // Hide league view, show team view
            document.getElementById('teams-container').style.display = 'none';
            document.querySelector('.page-header').style.display = 'none';
            document.getElementById('team-view').classList.add('active');

            // Update team view header
            document.getElementById('team-view-name').textContent = team.name;
            document.getElementById('team-view-emoji').textContent = getTeamEmoji(teamCode);

            // Update salary breakdown
            renderSalaryBreakdown(team);

            // Update players and draft picks
            renderTeamPlayers(team);
            renderTeamDraftPicks(team);
        }

        // Show league view (back from team view)
        function showLeagueView() {
            document.getElementById('team-view').classList.remove('active');
            document.getElementById('teams-container').style.display = 'grid';
            document.querySelector('.page-header').style.display = 'block';
        }

        // Render salary breakdown cards
        function renderSalaryBreakdown(team) {
            const container = document.getElementById('salary-breakdown');
            const isOverCap = team.salaryCapUsed > team.salaryCapLimit;
            const capSpace = team.salaryCapLimit - team.salaryCapUsed;
            const capPercentage = (team.salaryCapUsed / team.salaryCapLimit) * 100;

            container.innerHTML = `
                <div class="breakdown-card">
                    <div class="breakdown-value">$${team.salaryCapUsed.toFixed(1)}M</div>
                    <div class="breakdown-label">Salary Used</div>
                </div>
                <div class="breakdown-card">
                    <div class="breakdown-value">$${team.salaryCapLimit}M</div>
                    <div class="breakdown-label">Salary Cap</div>
                </div>
                <div class="breakdown-card">
                    <div class="breakdown-value ${isOverCap ? 'text-red-400' : 'text-green-400'}">
                        $${Math.abs(capSpace).toFixed(1)}M
                    </div>
                    <div class="breakdown-label">${isOverCap ? 'Over Cap' : 'Cap Space'}</div>
                </div>
                <div class="breakdown-card">
                    <div class="breakdown-value">${capPercentage.toFixed(1)}%</div>
                    <div class="breakdown-label">Cap Usage</div>
                </div>
            `;
        }

        // Render team players in detailed view
        function renderTeamPlayers(team) {
            const container = document.getElementById('active-players-list');
            const countElement = document.getElementById('active-player-count');

            countElement.textContent = `${team.players.length} players`;

            if (team.players.length === 0) {
                container.innerHTML = '<div class="empty-state"><p>No active players</p></div>';
                return;
            }

            const playersHTML = team.players
                .sort((a, b) => b.salary - a.salary)
                .map(player => `
                    <div class="player-item-detailed">
                        <div class="player-info">
                            <div class="player-name">${player.name}</div>
                            <div class="player-position">${player.position}</div>
                        </div>
                        <div class="player-salary">$${player.salary.toFixed(1)}M</div>
                        <div class="contract-year">2026</div>
                        <div class="player-status">Active</div>
                    </div>
                `).join('');

            container.innerHTML = playersHTML;
        }

        // Render team draft picks
        function renderTeamDraftPicks(team) {
            const container = document.getElementById('draft-picks-list');
            const countElement = document.getElementById('draft-pick-count');

            const picks = team.draftPicks || [];
            countElement.textContent = `${picks.length} picks`;

            if (picks.length === 0) {
                container.innerHTML = '<div class="empty-state"><p>No draft picks</p></div>';
                return;
            }

            const picksHTML = picks.map(pick => `
                <div class="draft-pick-item">
                    <div class="pick-info">
                        <div class="pick-year">${pick.year} ${pick.round}</div>
                        <div class="pick-details">${pick.details || pick.name}</div>
                    </div>
                    <div class="pick-value">$${pick.salary.toFixed(1)}M</div>
                </div>
            `).join('');

            container.innerHTML = picksHTML;
        }

        // Get team emoji
        function getTeamEmoji(teamCode) {
            const emojis = {
                'ATL': '🦅', 'BOS': '🍀', 'BRK': '🌉', 'CHA': '🐝', 'CHI': '🐂',
                'CLE': '⚔️', 'DAL': '🤠', 'DEN': '⛰️', 'DET': '🚗', 'GSW': '🌉',
                'HOU': '🚀', 'IND': '🏎️', 'LAC': '📎', 'LAL': '💜', 'MEM': '🐻',
                'MIA': '🔥', 'MIL': '🦌', 'MIN': '🐺', 'NOP': '⚜️', 'NYK': '🗽',
                'OKC': '⚡', 'ORL': '🪄', 'PHI': '🔔', 'PHX': '☀️', 'POR': '🌲',
                'SAC': '👑', 'SAS': '⭐', 'TOR': '🦖', 'UTA': '🏔️', 'WAS': '🏛️'
            };
            return emojis[teamCode] || '🏀';
        }

        // Trade Machine Functions
        function openTradeMachine() {
            if (Object.keys(leagueData).length === 0) {
                showStatus('❌ Please load league data first', 'error');
                return;
            }

            document.getElementById('trade-machine').classList.add('active');
            populateTeamSelectors();
            resetTrade();
        }

        function closeTradeMachine() {
            document.getElementById('trade-machine').classList.remove('active');
            resetTrade();
        }

        function populateTeamSelectors() {
            const selectorA = document.getElementById('trade-team-a-selector');
            const selectorB = document.getElementById('trade-team-b-selector');

            // Clear existing options
            selectorA.innerHTML = '<option value="">Choose Team...</option>';
            selectorB.innerHTML = '<option value="">Choose Team...</option>';

            // Add team options
            Object.values(leagueData)
                .sort((a, b) => a.name.localeCompare(b.name))
                .forEach(team => {
                    const optionA = document.createElement('option');
                    optionA.value = team.code;
                    optionA.textContent = team.name;
                    selectorA.appendChild(optionA);

                    const optionB = document.createElement('option');
                    optionB.value = team.code;
                    optionB.textContent = team.name;
                    selectorB.appendChild(optionB);
                });
        }

        function selectTradeTeam(side, teamCode) {
            if (!teamCode) return;

            const team = leagueData[teamCode];
            if (!team) return;

            if (side === 'A') {
                tradeState.teamA = teamCode;
                tradeState.originalTeamA = JSON.parse(JSON.stringify(team)); // Deep copy
                document.getElementById('trade-team-a-name').textContent = team.name;
                renderTradeTeamPlayers('A', team);
                updateTradeSalaryInfo('A');
            } else {
                tradeState.teamB = teamCode;
                tradeState.originalTeamB = JSON.parse(JSON.stringify(team)); // Deep copy
                document.getElementById('trade-team-b-name').textContent = team.name;
                renderTradeTeamPlayers('B', team);
                updateTradeSalaryInfo('B');
            }

            updateTradeAnalysis();
        }

        function renderTradeTeamPlayers(side, team) {
            const container = document.getElementById(`trade-team-${side.toLowerCase()}-players`);

            if (!team.players || team.players.length === 0) {
                container.innerHTML = '<div class="drop-zone-empty">No players available</div>';
                return;
            }

            const playersHTML = team.players
                .sort((a, b) => (b.adjustedSalary || b.salary) - (a.adjustedSalary || a.salary))
                .map(player => `
                    <div class="trade-player-item"
                         draggable="true"
                         ondragstart="dragTradePlayer(event, '${player.id}', '${side}')"
                         data-player-id="${player.id}">
                        <div class="trade-player-info">
                            <div class="trade-player-name">${player.name}</div>
                            <div class="trade-player-position">${player.position}</div>
                        </div>
                        <div class="trade-player-salary">
                            $${(player.adjustedSalary || player.salary).toFixed(1)}M
                            ${player.adjustment ? `<div style="font-size: 0.75rem; color: #64748b;">(${player.adjustment > 0 ? '+' : ''}${player.adjustment.toFixed(1)})</div>` : ''}
                        </div>
                    </div>
                `).join('');

            container.innerHTML = playersHTML;
        }

        function dragTradePlayer(event, playerId, fromSide) {
            event.dataTransfer.setData('text/plain', JSON.stringify({
                playerId: playerId,
                fromSide: fromSide
            }));
            event.target.classList.add('dragging');
        }

        function allowTradeDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.add('drag-over');
        }

        function dropPlayerInTrade(event, toSide) {
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');

            const data = JSON.parse(event.dataTransfer.getData('text/plain'));
            const { playerId, fromSide } = data;

            // Don't allow dropping on the same side
            if (fromSide === toSide) return;

            movePlayerInTrade(playerId, fromSide, toSide);

            // Remove dragging class
            document.querySelectorAll('.trade-player-item.dragging').forEach(el => {
                el.classList.remove('dragging');
            });
        }

        function movePlayerInTrade(playerId, fromSide, toSide) {
            const fromTeamCode = fromSide === 'A' ? tradeState.teamA : tradeState.teamB;
            const toTeamCode = toSide === 'A' ? tradeState.teamA : tradeState.teamB;

            if (!fromTeamCode || !toTeamCode) return;

            const fromTeam = leagueData[fromTeamCode];
            const toTeam = leagueData[toTeamCode];

            // Find and move the player
            const playerIndex = fromTeam.players.findIndex(p => p.id === playerId);
            if (playerIndex === -1) return;

            const player = fromTeam.players[playerIndex];

            // Remove from source team
            fromTeam.players.splice(playerIndex, 1);
            fromTeam.totalSalary -= player.salary;
            fromTeam.salaryCapUsed -= player.salary;

            // Add to destination team
            player.team = toTeamCode;
            toTeam.players.push(player);
            toTeam.totalSalary += player.salary;
            toTeam.salaryCapUsed += player.salary;

            // Track the trade
            if (toSide === 'A') {
                tradeState.playersA.push(player);
            } else {
                tradeState.playersB.push(player);
            }

            // Re-render both teams
            renderTradeTeamPlayers('A', leagueData[tradeState.teamA]);
            renderTradeTeamPlayers('B', leagueData[tradeState.teamB]);

            // Update salary info
            updateTradeSalaryInfo('A');
            updateTradeSalaryInfo('B');
            updateTradeAnalysis();

            console.log(`🔄 Moved ${player.name} from ${fromTeamCode} to ${toTeamCode}`);
        }

        function updateTradeSalaryInfo(side) {
            const teamCode = side === 'A' ? tradeState.teamA : tradeState.teamB;
            if (!teamCode) return;

            const team = leagueData[teamCode];
            const original = side === 'A' ? tradeState.originalTeamA : tradeState.originalTeamB;

            const currentSalary = original.salaryCapUsed;
            const afterTradeSalary = team.salaryCapUsed;
            const capSpace = team.salaryCapLimit - afterTradeSalary;

            document.getElementById(`trade-team-${side.toLowerCase()}-current`).textContent = `$${currentSalary.toFixed(1)}M`;
            document.getElementById(`trade-team-${side.toLowerCase()}-after`).textContent = `$${afterTradeSalary.toFixed(1)}M`;

            const spaceElement = document.getElementById(`trade-team-${side.toLowerCase()}-space`);
            spaceElement.textContent = `$${Math.abs(capSpace).toFixed(1)}M`;
            spaceElement.className = capSpace >= 0 ? 'trade-impact-positive' : 'trade-impact-negative';
        }

        function updateTradeAnalysis() {
            const hasPlayers = tradeState.playersA.length > 0 || tradeState.playersB.length > 0;

            if (!hasPlayers || !tradeState.teamA || !tradeState.teamB) {
                document.getElementById('trade-summary').style.display = 'none';
                document.getElementById('execute-trade-btn').disabled = true;
                return;
            }

            document.getElementById('trade-summary').style.display = 'block';
            document.getElementById('execute-trade-btn').disabled = false;

            const teamA = leagueData[tradeState.teamA];
            const teamB = leagueData[tradeState.teamB];
            const originalA = tradeState.originalTeamA;
            const originalB = tradeState.originalTeamB;

            const salaryChangeA = teamA.salaryCapUsed - originalA.salaryCapUsed;
            const salaryChangeB = teamB.salaryCapUsed - originalB.salaryCapUsed;

            const impactHTML = `
                <div class="trade-impact-team">
                    <h4>${teamA.name}</h4>
                    <div class="trade-impact-row">
                        <span>Players Acquired:</span>
                        <span>${tradeState.playersA.length}</span>
                    </div>
                    <div class="trade-impact-row">
                        <span>Players Traded:</span>
                        <span>${tradeState.playersB.length}</span>
                    </div>
                    <div class="trade-impact-row">
                        <span>Salary Change:</span>
                        <span class="${salaryChangeA >= 0 ? 'trade-impact-negative' : 'trade-impact-positive'}">
                            ${salaryChangeA >= 0 ? '+' : ''}$${salaryChangeA.toFixed(1)}M
                        </span>
                    </div>
                    <div class="trade-impact-row">
                        <span>New Cap Space:</span>
                        <span class="${(teamA.salaryCapLimit - teamA.salaryCapUsed) >= 0 ? 'trade-impact-positive' : 'trade-impact-negative'}">
                            $${(teamA.salaryCapLimit - teamA.salaryCapUsed).toFixed(1)}M
                        </span>
                    </div>
                </div>
                <div class="trade-impact-team">
                    <h4>${teamB.name}</h4>
                    <div class="trade-impact-row">
                        <span>Players Acquired:</span>
                        <span>${tradeState.playersB.length}</span>
                    </div>
                    <div class="trade-impact-row">
                        <span>Players Traded:</span>
                        <span>${tradeState.playersA.length}</span>
                    </div>
                    <div class="trade-impact-row">
                        <span>Salary Change:</span>
                        <span class="${salaryChangeB >= 0 ? 'trade-impact-negative' : 'trade-impact-positive'}">
                            ${salaryChangeB >= 0 ? '+' : ''}$${salaryChangeB.toFixed(1)}M
                        </span>
                    </div>
                    <div class="trade-impact-row">
                        <span>New Cap Space:</span>
                        <span class="${(teamB.salaryCapLimit - teamB.salaryCapUsed) >= 0 ? 'trade-impact-positive' : 'trade-impact-negative'}">
                            $${(teamB.salaryCapLimit - teamB.salaryCapUsed).toFixed(1)}M
                        </span>
                    </div>
                </div>
            `;

            document.getElementById('trade-impact').innerHTML = impactHTML;
        }

        function resetTrade() {
            // Reset trade state
            tradeState = {
                teamA: null,
                teamB: null,
                playersA: [],
                playersB: [],
                originalTeamA: null,
                originalTeamB: null
            };

            // Reset UI
            document.getElementById('trade-team-a-selector').value = '';
            document.getElementById('trade-team-b-selector').value = '';
            document.getElementById('trade-team-a-name').textContent = 'Select Team A';
            document.getElementById('trade-team-b-name').textContent = 'Select Team B';

            // Reset player lists
            document.getElementById('trade-team-a-players').innerHTML = '<div class="drop-zone-empty">Select a team to see available players</div>';
            document.getElementById('trade-team-b-players').innerHTML = '<div class="drop-zone-empty">Select a team to see available players</div>';

            // Reset salary info
            ['a', 'b'].forEach(side => {
                document.getElementById(`trade-team-${side}-current`).textContent = '$0.0M';
                document.getElementById(`trade-team-${side}-after`).textContent = '$0.0M';
                document.getElementById(`trade-team-${side}-space`).textContent = '$0.0M';
            });

            // Hide trade summary
            document.getElementById('trade-summary').style.display = 'none';
            document.getElementById('execute-trade-btn').disabled = true;

            // Restore original team data
            if (Object.keys(leagueData).length > 0) {
                loadLeagueData(); // Reload to reset any changes
            }
        }

        function executeTrade() {
            if (!tradeState.teamA || !tradeState.teamB || (tradeState.playersA.length === 0 && tradeState.playersB.length === 0)) {
                showStatus('❌ No valid trade to execute', 'error');
                return;
            }

            // The trade has already been applied to the data during drag/drop
            // Just update the UI and show confirmation
            renderLeague();
            closeTradeMachine();

            const playersANames = tradeState.playersA.map(p => p.name).join(', ');
            const playersBNames = tradeState.playersB.map(p => p.name).join(', ');
            const teamAName = leagueData[tradeState.teamA].name;
            const teamBName = leagueData[tradeState.teamB].name;

            let tradeMessage = '✅ Trade executed successfully!\n\n';
            if (tradeState.playersA.length > 0) {
                tradeMessage += `${teamAName} receives: ${playersANames}\n`;
            }
            if (tradeState.playersB.length > 0) {
                tradeMessage += `${teamBName} receives: ${playersBNames}`;
            }

            showStatus(tradeMessage, 'success');
        }
    </script>
</body>
</html>
